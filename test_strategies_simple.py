import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_and_test():
    """简化的策略测试"""
    # 数据路径
    data_path = "/Users/<USER>/Downloads/20241203IC/20241203IC_20250617.csv"
    
    print("正在加载数据...")
    try:
        df = pd.read_csv(data_path)
        print(f"数据加载成功，共 {len(df)} 行数据")
        
        # 转换时间列
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # 使用最近的数据进行测试（最后30000行，大约1个月的数据）
        test_df = df.tail(30000).copy()
        print(f"使用最近 {len(test_df)} 行数据进行测试")
        print(f"测试数据时间范围: {test_df['datetime'].min()} 到 {test_df['datetime'].max()}")
        
        # 计算基础指标
        close_prices = test_df['close'].values
        
        # Buy and Hold策略
        bh_returns = []
        for i in range(1, len(close_prices)):
            if close_prices[i-1] != 0:
                ret = (close_prices[i] - close_prices[i-1]) / close_prices[i-1]
                bh_returns.append(ret)
            else:
                bh_returns.append(0)
        
        # 计算绩效指标
        bh_returns = np.array(bh_returns)
        total_return = np.prod(1 + bh_returns) - 1
        
        # 年化收益率
        periods_per_year = 252 * 48  # 每年252个交易日，每天48个5分钟K线
        annualized_return = (1 + total_return) ** (periods_per_year / len(bh_returns)) - 1
        
        # 夏普比率
        risk_free_rate = 0.03
        excess_returns = bh_returns - risk_free_rate / periods_per_year
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(periods_per_year) if excess_returns.std() != 0 else 0
        
        # 最大回撤
        cumulative_returns = np.cumprod(1 + bh_returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        # 胜率
        positive_returns = bh_returns[bh_returns > 0]
        win_rate = len(positive_returns) / len(bh_returns) if len(bh_returns) > 0 else 0
        
        print(f"\n=== Buy and Hold策略绩效 ===")
        print(f"总收益率: {total_return:.4f}")
        print(f"年化收益率: {annualized_return:.4f}")
        print(f"夏普比率: {sharpe_ratio:.4f}")
        print(f"最大回撤: {max_drawdown:.4f}")
        print(f"胜率: {win_rate:.4f}")
        
        # 简单移动平均策略
        short_window = 5
        long_window = 20
        
        test_df['ma_short'] = test_df['close'].rolling(window=short_window).mean()
        test_df['ma_long'] = test_df['close'].rolling(window=long_window).mean()
        
        # 生成交易信号
        signals = []
        position = 0
        
        for i in range(long_window, len(test_df)):
            if pd.isna(test_df['ma_short'].iloc[i]) or pd.isna(test_df['ma_long'].iloc[i]):
                signals.append(0)
                continue
                
            # 金叉买入
            if (test_df['ma_short'].iloc[i] > test_df['ma_long'].iloc[i] and 
                test_df['ma_short'].iloc[i-1] <= test_df['ma_long'].iloc[i-1] and position == 0):
                signals.append(1)
                position = 1
            # 死叉卖出
            elif (test_df['ma_short'].iloc[i] < test_df['ma_long'].iloc[i] and 
                  test_df['ma_short'].iloc[i-1] >= test_df['ma_long'].iloc[i-1] and position == 1):
                signals.append(-1)
                position = 0
            else:
                signals.append(0)
        
        # 计算策略收益
        strategy_returns = []
        current_position = 0
        
        for i, signal in enumerate(signals):
            if signal == 1:
                current_position = 1
            elif signal == -1:
                current_position = 0
            
            # 计算当期收益
            if i + long_window + 1 < len(test_df):
                price_return = ((test_df['close'].iloc[i + long_window + 1] - 
                               test_df['close'].iloc[i + long_window]) / 
                               test_df['close'].iloc[i + long_window])
                strategy_return = price_return * current_position
                strategy_returns.append(strategy_return)
        
        # 计算移动平均策略绩效
        if len(strategy_returns) > 0:
            strategy_returns = np.array(strategy_returns)
            ma_total_return = np.prod(1 + strategy_returns) - 1
            ma_annualized_return = (1 + ma_total_return) ** (periods_per_year / len(strategy_returns)) - 1
            
            ma_excess_returns = strategy_returns - risk_free_rate / periods_per_year
            ma_sharpe_ratio = (ma_excess_returns.mean() / ma_excess_returns.std() * 
                              np.sqrt(periods_per_year) if ma_excess_returns.std() != 0 else 0)
            
            ma_cumulative_returns = np.cumprod(1 + strategy_returns)
            ma_running_max = np.maximum.accumulate(ma_cumulative_returns)
            ma_drawdown = (ma_cumulative_returns - ma_running_max) / ma_running_max
            ma_max_drawdown = np.min(ma_drawdown)
            
            ma_positive_returns = strategy_returns[strategy_returns > 0]
            ma_win_rate = len(ma_positive_returns) / len(strategy_returns)
            
            trade_count = sum(1 for s in signals if s != 0)
            trading_days = len(signals) / 48
            trade_frequency = trade_count / trading_days if trading_days > 0 else 0
            
            print(f"\n=== 移动平均交叉策略绩效 ===")
            print(f"总收益率: {ma_total_return:.4f}")
            print(f"年化收益率: {ma_annualized_return:.4f}")
            print(f"夏普比率: {ma_sharpe_ratio:.4f}")
            print(f"最大回撤: {ma_max_drawdown:.4f}")
            print(f"胜率: {ma_win_rate:.4f}")
            print(f"日均交易频率: {trade_frequency:.2f}")
            
            print(f"\n=== 策略对比 ===")
            print(f"{'指标':<15} {'Buy & Hold':<15} {'MA交叉策略':<15}")
            print("-" * 45)
            print(f"{'年化收益率':<15} {annualized_return:<15.4f} {ma_annualized_return:<15.4f}")
            print(f"{'夏普比率':<15} {sharpe_ratio:<15.4f} {ma_sharpe_ratio:<15.4f}")
            print(f"{'最大回撤':<15} {max_drawdown:<15.4f} {ma_max_drawdown:<15.4f}")
            print(f"{'胜率':<15} {win_rate:<15.4f} {ma_win_rate:<15.4f}")
            
            # 判断哪个策略更好
            if ma_sharpe_ratio > sharpe_ratio and ma_sharpe_ratio > 1.5:
                print(f"\n✅ 移动平均交叉策略表现更好，夏普比率达到 {ma_sharpe_ratio:.4f} > 1.5")
            elif sharpe_ratio > 1.5:
                print(f"\n✅ Buy and Hold策略表现更好，夏普比率达到 {sharpe_ratio:.4f} > 1.5")
            else:
                print(f"\n⚠️  两个策略的夏普比率都未达到1.5的目标")
                print(f"   需要进一步优化策略参数或尝试其他策略")
        
        return test_df, signals
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    test_df, signals = load_and_test()
