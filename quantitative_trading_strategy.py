import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DataAnalyzer:
    def __init__(self, data_path):
        """初始化数据分析器"""
        self.data_path = data_path
        self.df = None
        
    def load_data(self):
        """加载CSV数据"""
        print(f"正在加载数据: {self.data_path}")
        try:
            self.df = pd.read_csv(self.data_path)
            print(f"数据加载成功，共 {len(self.df)} 行数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def analyze_data_structure(self):
        """分析数据结构"""
        if self.df is None:
            print("请先加载数据")
            return
        
        print("\n=== 数据结构分析 ===")
        print(f"数据形状: {self.df.shape}")
        print(f"列名: {list(self.df.columns)}")
        print("\n数据类型:")
        print(self.df.dtypes)
        print("\n前5行数据:")
        print(self.df.head())
        print("\n数据统计信息:")
        print(self.df.describe())
        
        # 检查缺失值
        missing_values = self.df.isnull().sum()
        if missing_values.sum() > 0:
            print("\n缺失值统计:")
            print(missing_values[missing_values > 0])
        else:
            print("\n无缺失值")
    
    def preprocess_data(self):
        """数据预处理"""
        if self.df is None:
            print("请先加载数据")
            return
        
        print("\n=== 数据预处理 ===")
        
        # 尝试识别时间列
        time_columns = []
        for col in self.df.columns:
            if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp']):
                time_columns.append(col)
        
        if time_columns:
            print(f"发现时间列: {time_columns}")
            # 转换第一个时间列
            time_col = time_columns[0]
            try:
                self.df[time_col] = pd.to_datetime(self.df[time_col])
                self.df = self.df.sort_values(time_col).reset_index(drop=True)
                print(f"时间列 {time_col} 转换成功")
                
                # 设置时间范围信息
                print(f"数据时间范围: {self.df[time_col].min()} 到 {self.df[time_col].max()}")
                print(f"数据总天数: {(self.df[time_col].max() - self.df[time_col].min()).days} 天")
                
            except Exception as e:
                print(f"时间列转换失败: {e}")
        
        # 识别OHLCV列 - 精确匹配
        ohlcv_mapping = {}
        for col in self.df.columns:
            if col == 'open':
                ohlcv_mapping['open'] = col
            elif col == 'high':
                ohlcv_mapping['high'] = col
            elif col == 'low':
                ohlcv_mapping['low'] = col
            elif col == 'close':
                ohlcv_mapping['close'] = col
            elif col == 'volume':
                ohlcv_mapping['volume'] = col

        print(f"OHLCV列映射: {ohlcv_mapping}")
        self.ohlcv_mapping = ohlcv_mapping
        
        return self.df
    
    def create_test_dataset(self, test_ratio=0.3):
        """创建测试集"""
        if self.df is None:
            print("请先加载数据")
            return None, None
        
        print(f"\n=== 创建测试集 (测试比例: {test_ratio}) ===")
        
        # 按时间顺序分割数据
        split_index = int(len(self.df) * (1 - test_ratio))
        train_df = self.df.iloc[:split_index].copy()
        test_df = self.df.iloc[split_index:].copy()
        
        print(f"训练集大小: {len(train_df)} 行")
        print(f"测试集大小: {len(test_df)} 行")
        
        if hasattr(self, 'ohlcv_mapping') and 'close' in self.ohlcv_mapping:
            time_col = None
            for col in self.df.columns:
                if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp']):
                    time_col = col
                    break
            
            if time_col:
                print(f"训练集时间范围: {train_df[time_col].min()} 到 {train_df[time_col].max()}")
                print(f"测试集时间范围: {test_df[time_col].min()} 到 {test_df[time_col].max()}")
        
        return train_df, test_df
    
    def plot_price_overview(self):
        """绘制价格概览图"""
        if self.df is None or not hasattr(self, 'ohlcv_mapping'):
            print("请先加载并预处理数据")
            return
        
        if 'close' not in self.ohlcv_mapping:
            print("未找到收盘价列")
            return
        
        # 找到时间列
        time_col = None
        for col in self.df.columns:
            if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp']):
                time_col = col
                break
        
        if time_col is None:
            print("未找到时间列")
            return
        
        plt.figure(figsize=(15, 8))
        
        # 绘制收盘价
        plt.subplot(2, 1, 1)
        plt.plot(self.df[time_col], self.df[self.ohlcv_mapping['close']], linewidth=0.8)
        plt.title('收盘价走势')
        plt.ylabel('价格')
        plt.grid(True, alpha=0.3)
        
        # 绘制成交量（如果有）
        if 'volume' in self.ohlcv_mapping:
            plt.subplot(2, 1, 2)
            plt.plot(self.df[time_col], self.df[self.ohlcv_mapping['volume']], linewidth=0.8, color='orange')
            plt.title('成交量')
            plt.ylabel('成交量')
            plt.xlabel('时间')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()


class StrategyBacktester:
    def __init__(self, data, ohlcv_mapping):
        """初始化回测器"""
        self.data = data.copy()
        self.ohlcv_mapping = ohlcv_mapping
        self.positions = []  # 持仓记录
        self.trades = []     # 交易记录
        self.portfolio_value = []  # 组合价值
        self.initial_capital = 1000000  # 初始资金100万

    def add_technical_indicators(self):
        """添加技术指标"""
        df = self.data.copy()

        # 确保有正确的OHLCV列
        if 'close' not in self.ohlcv_mapping:
            print("错误：未找到收盘价列")
            return df

        close_col = self.ohlcv_mapping['close']
        volume_col = self.ohlcv_mapping.get('volume', 'volume')

        # 移动平均线
        df['ma5'] = df[close_col].rolling(window=5).mean()
        df['ma10'] = df[close_col].rolling(window=10).mean()
        df['ma20'] = df[close_col].rolling(window=20).mean()
        df['ma60'] = df[close_col].rolling(window=60).mean()

        # RSI
        delta = df[close_col].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # 布林带
        df['bb_middle'] = df[close_col].rolling(window=20).mean()
        bb_std = df[close_col].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']

        # MACD
        exp1 = df[close_col].ewm(span=12).mean()
        exp2 = df[close_col].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']

        # 成交量指标
        if volume_col in df.columns:
            df['volume_ma'] = df[volume_col].rolling(window=20).mean()
            df['volume_ratio'] = df[volume_col] / df['volume_ma']

        # 价格变化率
        df['price_change'] = df[close_col].pct_change()
        df['price_change_5'] = df[close_col].pct_change(5)

        # 波动率
        df['volatility'] = df['price_change'].rolling(window=20).std()

        self.data = df
        return df

    def calculate_performance_metrics(self, returns, benchmark_returns=None):
        """计算绩效指标"""
        if len(returns) == 0:
            return {}

        returns = pd.Series(returns)

        # 基础统计
        total_return = (1 + returns).prod() - 1

        # 年化收益率（假设每年252个交易日，每天48个5分钟K线）
        periods_per_year = 252 * 48
        annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1

        # 夏普比率
        risk_free_rate = 0.03  # 假设无风险利率3%
        excess_returns = returns - risk_free_rate / periods_per_year
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(periods_per_year) if excess_returns.std() != 0 else 0

        # 最大回撤
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()

        # 最大回撤恢复时间
        max_dd_recovery_time = 0
        if max_drawdown < 0:
            dd_start = drawdown.idxmin()
            recovery_series = cumulative_returns[dd_start:]
            peak_value = running_max.loc[dd_start]
            recovery_idx = recovery_series[recovery_series >= peak_value].index
            if len(recovery_idx) > 0:
                max_dd_recovery_time = recovery_idx[0] - dd_start

        # 胜率计算
        positive_returns = returns[returns > 0]
        daily_win_rate = len(positive_returns) / len(returns) if len(returns) > 0 else 0

        # 周胜率（假设每周5个交易日）
        weekly_returns = returns.groupby(returns.index // (5 * 48)).sum()
        weekly_positive = weekly_returns[weekly_returns > 0]
        weekly_win_rate = len(weekly_positive) / len(weekly_returns) if len(weekly_returns) > 0 else 0

        # 月胜率（假设每月22个交易日）
        monthly_returns = returns.groupby(returns.index // (22 * 48)).sum()
        monthly_positive = monthly_returns[monthly_returns > 0]
        monthly_win_rate = len(monthly_positive) / len(monthly_returns) if len(monthly_returns) > 0 else 0

        # Calmar比率
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0

        metrics = {
            'Total Return': total_return,
            'Annualized Return': annualized_return,
            'Sharpe Ratio': sharpe_ratio,
            'Max Drawdown': max_drawdown,
            'Max Drawdown Recovery Time': max_dd_recovery_time,
            'Daily Win Rate': daily_win_rate,
            'Weekly Win Rate': weekly_win_rate,
            'Monthly Win Rate': monthly_win_rate,
            'Calmar Ratio': calmar_ratio,
            'Volatility': returns.std() * np.sqrt(periods_per_year)
        }

        return metrics

    def buy_and_hold_strategy(self):
        """买入持有策略"""
        if 'close' not in self.ohlcv_mapping:
            print("错误：未找到收盘价列")
            return [], {}

        close_col = self.ohlcv_mapping['close']
        prices = self.data[close_col].values

        # 计算收益率
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                returns.append((prices[i] - prices[i-1]) / prices[i-1])
            else:
                returns.append(0)

        # 计算绩效指标
        metrics = self.calculate_performance_metrics(returns)

        # 添加交易频率（买入持有策略交易频率为0）
        metrics['Average Daily Trading Frequency'] = 0

        return returns, metrics


class TradingStrategy:
    def __init__(self, backtester):
        """初始化交易策略"""
        self.backtester = backtester
        self.data = backtester.data
        self.ohlcv_mapping = backtester.ohlcv_mapping

    def moving_average_crossover_strategy(self, short_window=5, long_window=20):
        """移动平均交叉策略"""
        if 'close' not in self.ohlcv_mapping:
            return [], {}, []

        close_col = self.ohlcv_mapping['close']

        # 计算移动平均
        short_ma = self.data[close_col].rolling(window=short_window).mean()
        long_ma = self.data[close_col].rolling(window=long_window).mean()

        # 生成交易信号
        signals = []
        positions = []
        current_position = 0  # 0: 空仓, 1: 多头

        for i in range(long_window, len(self.data)):
            if pd.isna(short_ma.iloc[i]) or pd.isna(long_ma.iloc[i]):
                signals.append(0)
                positions.append(current_position)
                continue

            # 金叉买入
            if short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i-1] <= long_ma.iloc[i-1] and current_position == 0:
                signals.append(1)  # 买入信号
                current_position = 1
            # 死叉卖出
            elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i-1] >= long_ma.iloc[i-1] and current_position == 1:
                signals.append(-1)  # 卖出信号
                current_position = 0
            else:
                signals.append(0)  # 无信号

            positions.append(current_position)

        # 计算收益率
        returns = self._calculate_strategy_returns(signals, positions)

        # 计算绩效指标
        metrics = self.backtester.calculate_performance_metrics(returns)

        # 计算交易频率
        trade_count = sum(1 for s in signals if s != 0)
        trading_days = len(signals) / 48  # 每天48个5分钟K线
        metrics['Average Daily Trading Frequency'] = trade_count / trading_days if trading_days > 0 else 0

        return returns, metrics, signals

    def _calculate_strategy_returns(self, signals, positions):
        """计算策略收益率"""
        if 'close' not in self.ohlcv_mapping:
            return []

        close_col = self.ohlcv_mapping['close']
        prices = self.data[close_col].values
        returns = []

        for i in range(1, len(signals) + 1):
            if i >= len(prices):
                break

            price_return = (prices[i] - prices[i-1]) / prices[i-1] if prices[i-1] != 0 else 0

            # 根据持仓计算收益
            if i-1 < len(positions):
                position = positions[i-1]
                strategy_return = price_return * position
            else:
                strategy_return = 0

            returns.append(strategy_return)

        return returns

    def rsi_strategy(self, rsi_oversold=30, rsi_overbought=70, rsi_period=14):
        """RSI策略"""
        if 'close' not in self.ohlcv_mapping:
            return [], {}, []

        close_col = self.ohlcv_mapping['close']

        # 计算RSI
        delta = self.data[close_col].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 生成交易信号
        signals = []
        positions = []
        current_position = 0

        for i in range(rsi_period, len(self.data)):
            if pd.isna(rsi.iloc[i]):
                signals.append(0)
                positions.append(current_position)
                continue

            # RSI超卖买入
            if rsi.iloc[i] < rsi_oversold and current_position == 0:
                signals.append(1)
                current_position = 1
            # RSI超买卖出
            elif rsi.iloc[i] > rsi_overbought and current_position == 1:
                signals.append(-1)
                current_position = 0
            else:
                signals.append(0)

            positions.append(current_position)

        returns = self._calculate_strategy_returns(signals, positions)
        metrics = self.backtester.calculate_performance_metrics(returns)

        trade_count = sum(1 for s in signals if s != 0)
        trading_days = len(signals) / 48
        metrics['Average Daily Trading Frequency'] = trade_count / trading_days if trading_days > 0 else 0

        return returns, metrics, signals

    def bollinger_bands_strategy(self, bb_period=20, bb_std=2):
        """布林带策略"""
        if 'close' not in self.ohlcv_mapping:
            return [], {}, []

        close_col = self.ohlcv_mapping['close']

        # 计算布林带
        bb_middle = self.data[close_col].rolling(window=bb_period).mean()
        bb_std_dev = self.data[close_col].rolling(window=bb_period).std()
        bb_upper = bb_middle + (bb_std_dev * bb_std)
        bb_lower = bb_middle - (bb_std_dev * bb_std)

        signals = []
        positions = []
        current_position = 0

        for i in range(bb_period, len(self.data)):
            if pd.isna(bb_upper.iloc[i]) or pd.isna(bb_lower.iloc[i]):
                signals.append(0)
                positions.append(current_position)
                continue

            price = self.data[close_col].iloc[i]

            # 价格触及下轨买入
            if price <= bb_lower.iloc[i] and current_position == 0:
                signals.append(1)
                current_position = 1
            # 价格触及上轨卖出
            elif price >= bb_upper.iloc[i] and current_position == 1:
                signals.append(-1)
                current_position = 0
            else:
                signals.append(0)

            positions.append(current_position)

        returns = self._calculate_strategy_returns(signals, positions)
        metrics = self.backtester.calculate_performance_metrics(returns)

        trade_count = sum(1 for s in signals if s != 0)
        trading_days = len(signals) / 48
        metrics['Average Daily Trading Frequency'] = trade_count / trading_days if trading_days > 0 else 0

        return returns, metrics, signals

def test_strategies():
    """测试策略函数"""
    # 数据路径
    data_path = "/Users/<USER>/Downloads/20241203IC/20241203IC_20250617.csv"

    # 创建数据分析器
    analyzer = DataAnalyzer(data_path)

    # 加载数据
    if not analyzer.load_data():
        return

    # 预处理数据
    analyzer.preprocess_data()

    # 创建测试集（使用较小的数据集进行快速测试）
    train_df, test_df = analyzer.create_test_dataset(test_ratio=0.7)  # 使用30%数据训练

    print(f"\n=== 使用训练集测试策略 ===")
    print(f"训练集大小: {len(train_df)} 行")

    # 创建回测器
    backtester = StrategyBacktester(train_df, analyzer.ohlcv_mapping)
    backtester.add_technical_indicators()

    print("\n=== 测试Buy and Hold策略 ===")
    bh_returns, bh_metrics = backtester.buy_and_hold_strategy()

    print("Buy and Hold策略绩效:")
    for key, value in bh_metrics.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")

    print("\n=== 测试多种交易策略 ===")
    strategy = TradingStrategy(backtester)

    # 测试移动平均交叉策略
    print("1. 移动平均交叉策略 (5,20):")
    ma_returns, ma_metrics, ma_signals = strategy.moving_average_crossover_strategy(short_window=5, long_window=20)
    print(f"   年化收益: {ma_metrics.get('Annualized Return', 0):.4f}, 夏普比率: {ma_metrics.get('Sharpe Ratio', 0):.4f}")

    # 测试RSI策略
    print("2. RSI策略 (30,70):")
    rsi_returns, rsi_metrics, rsi_signals = strategy.rsi_strategy(rsi_oversold=30, rsi_overbought=70)
    print(f"   年化收益: {rsi_metrics.get('Annualized Return', 0):.4f}, 夏普比率: {rsi_metrics.get('Sharpe Ratio', 0):.4f}")

    # 测试布林带策略
    print("3. 布林带策略 (20,2):")
    bb_returns, bb_metrics, bb_signals = strategy.bollinger_bands_strategy(bb_period=20, bb_std=2)
    print(f"   年化收益: {bb_metrics.get('Annualized Return', 0):.4f}, 夏普比率: {bb_metrics.get('Sharpe Ratio', 0):.4f}")

    # 收集所有策略结果
    strategies_results = {
        'Buy & Hold': bh_metrics,
        'MA交叉(5,20)': ma_metrics,
        'RSI(30,70)': rsi_metrics,
        '布林带(20,2)': bb_metrics
    }

    print(f"\n=== 策略详细对比 ===")
    print(f"{'策略':<15} {'年化收益':<12} {'夏普比率':<12} {'最大回撤':<12} {'月胜率':<12} {'交易频率':<12}")
    print("-" * 75)

    for strategy_name, metrics in strategies_results.items():
        ann_ret = metrics.get('Annualized Return', 0)
        sharpe = metrics.get('Sharpe Ratio', 0)
        max_dd = metrics.get('Max Drawdown', 0)
        monthly_wr = metrics.get('Monthly Win Rate', 0)
        trade_freq = metrics.get('Average Daily Trading Frequency', 0)

        print(f"{strategy_name:<15} {ann_ret:<12.4f} {sharpe:<12.4f} {max_dd:<12.4f} {monthly_wr:<12.4f} {trade_freq:<12.2f}")

    # 找出最佳策略
    best_strategy = None
    best_sharpe = -999
    for name, metrics in strategies_results.items():
        sharpe = metrics.get('Sharpe Ratio', -999)
        if sharpe > best_sharpe:
            best_sharpe = sharpe
            best_strategy = name

    print(f"\n=== 最佳策略 ===")
    print(f"基于夏普比率，最佳策略是: {best_strategy} (夏普比率: {best_sharpe:.4f})")

    return analyzer, backtester, strategy, strategies_results

def main():
    """主函数"""
    return test_strategies()

if __name__ == "__main__":
    results = main()
