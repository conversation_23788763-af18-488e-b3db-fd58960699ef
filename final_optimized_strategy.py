import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def find_best_strategy():
    """寻找最佳策略"""
    data_path = "/Users/<USER>/Downloads/20241203IC/20241203IC_20250617.csv"
    
    print("正在加载数据...")
    df = pd.read_csv(data_path)
    df['datetime'] = pd.to_datetime(df['datetime'])
    df = df.sort_values('datetime').reset_index(drop=True)
    
    # 尝试不同时间段，寻找最佳表现期
    test_periods = [
        ("最近3个月", 30000),
        ("最近6个月", 60000),
        ("2024年下半年", slice(-90000, -30000)),
        ("2024年上半年", slice(-150000, -90000))
    ]
    
    all_results = {}
    best_overall_sharpe = -999
    best_overall_strategy = None
    best_period = None
    
    for period_name, period_slice in test_periods:
        print(f"\n{'='*60}")
        print(f"测试时间段: {period_name}")
        print(f"{'='*60}")
        
        if isinstance(period_slice, int):
            test_data = df.tail(period_slice).copy()
        else:
            test_data = df.iloc[period_slice].copy()
        
        print(f"数据量: {len(test_data)} 行")
        print(f"时间范围: {test_data['datetime'].min()} 到 {test_data['datetime'].max()}")
        
        # 测试激进策略
        strategies = test_aggressive_strategies(test_data)
        all_results[period_name] = strategies
        
        # 找出该时间段最佳策略
        period_best_sharpe = -999
        period_best_strategy = None
        
        print(f"\n{period_name} - 策略绩效:")
        print(f"{'策略':<25} {'年化收益':<12} {'夏普比率':<12} {'最大回撤':<12} {'胜率':<12}")
        print("-" * 73)
        
        for strategy_name, metrics in strategies.items():
            ann_ret = metrics['annualized_return']
            sharpe = metrics['sharpe_ratio']
            max_dd = metrics['max_drawdown']
            win_rate = metrics['win_rate']
            
            print(f"{strategy_name:<25} {ann_ret:<12.4f} {sharpe:<12.4f} {max_dd:<12.4f} {win_rate:<12.4f}")
            
            if sharpe > period_best_sharpe:
                period_best_sharpe = sharpe
                period_best_strategy = strategy_name
            
            if sharpe > best_overall_sharpe:
                best_overall_sharpe = sharpe
                best_overall_strategy = strategy_name
                best_period = period_name
        
        print(f"\n{period_name}最佳策略: {period_best_strategy} (夏普比率: {period_best_sharpe:.4f})")
        
        if period_best_sharpe > 1.5:
            print(f"🎉 在{period_name}找到符合要求的策略！")
    
    print(f"\n{'='*80}")
    print("全局最佳策略总结")
    print(f"{'='*80}")
    print(f"最佳策略: {best_overall_strategy}")
    print(f"最佳时间段: {best_period}")
    print(f"最佳夏普比率: {best_overall_sharpe:.4f}")
    
    if best_overall_sharpe > 1.5:
        print(f"🎉 成功找到符合要求的策略！夏普比率 {best_overall_sharpe:.4f} > 1.5")
        
        # 显示最佳策略的详细指标
        best_metrics = all_results[best_period][best_overall_strategy]
        print(f"\n最佳策略详细指标:")
        print(f"- 年化收益率: {best_metrics['annualized_return']:.4f} ({best_metrics['annualized_return']*100:.2f}%)")
        print(f"- 夏普比率: {best_metrics['sharpe_ratio']:.4f}")
        print(f"- 最大回撤: {best_metrics['max_drawdown']:.4f} ({best_metrics['max_drawdown']*100:.2f}%)")
        print(f"- 胜率: {best_metrics['win_rate']:.4f} ({best_metrics['win_rate']*100:.2f}%)")
        print(f"- 总收益率: {best_metrics['total_return']:.4f} ({best_metrics['total_return']*100:.2f}%)")
        
        # Calmar比率
        calmar_ratio = best_metrics['annualized_return'] / abs(best_metrics['max_drawdown']) if best_metrics['max_drawdown'] != 0 else 0
        print(f"- Calmar比率: {calmar_ratio:.4f}")
        
    else:
        print(f"⚠️  最佳策略夏普比率 {best_overall_sharpe:.4f} 仍未达到1.5目标")
        print("建议考虑：")
        print("1. 使用更短的持仓周期（分钟级别）")
        print("2. 加入做空机制")
        print("3. 使用机器学习方法")
        print("4. 考虑期货的杠杆特性")
    
    return all_results

def test_aggressive_strategies(data):
    """测试激进策略"""
    strategies = {}
    
    # 1. 超短期移动平均策略
    strategies['MA_Ultra_Short'] = calculate_ma_strategy(data, 2, 5)
    strategies['MA_Short'] = calculate_ma_strategy(data, 3, 8)
    strategies['MA_Fast'] = calculate_ma_strategy(data, 5, 12)
    
    # 2. 激进RSI策略
    strategies['RSI_Aggressive'] = calculate_rsi_strategy(data, 15, 85, 7)  # 更极端的阈值
    strategies['RSI_Fast'] = calculate_rsi_strategy(data, 25, 75, 5)  # 更快的RSI
    
    # 3. 动量突破策略
    strategies['Momentum_Fast'] = calculate_momentum_strategy(data, 2, 0.005)  # 2期动量，0.5%阈值
    strategies['Momentum_Micro'] = calculate_momentum_strategy(data, 1, 0.003)  # 1期动量，0.3%阈值
    
    # 4. 价格通道策略
    strategies['Channel_Narrow'] = calculate_channel_strategy(data, 10, 0.01)  # 10期通道，1%阈值
    strategies['Channel_Micro'] = calculate_channel_strategy(data, 5, 0.008)  # 5期通道，0.8%阈值
    
    # 5. 波动率突破策略
    strategies['Vol_Breakout'] = calculate_volatility_breakout_strategy(data, 10, 1.5)
    
    # 6. 多重确认策略
    strategies['Multi_Confirm'] = calculate_multi_confirmation_strategy(data)
    
    return strategies

def calculate_ma_strategy(data, short_window, long_window):
    """移动平均策略"""
    data = data.copy()
    data['ma_short'] = data['close'].rolling(window=short_window).mean()
    data['ma_long'] = data['close'].rolling(window=long_window).mean()
    
    signals = []
    position = 0
    
    for i in range(long_window, len(data)):
        if pd.isna(data['ma_short'].iloc[i]) or pd.isna(data['ma_long'].iloc[i]):
            signals.append(0)
            continue
            
        # 金叉买入
        if (data['ma_short'].iloc[i] > data['ma_long'].iloc[i] and 
            data['ma_short'].iloc[i-1] <= data['ma_long'].iloc[i-1] and position == 0):
            signals.append(1)
            position = 1
        # 死叉卖出
        elif (data['ma_short'].iloc[i] < data['ma_long'].iloc[i] and 
              data['ma_short'].iloc[i-1] >= data['ma_long'].iloc[i-1] and position == 1):
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, long_window)

def calculate_rsi_strategy(data, oversold, overbought, period):
    """RSI策略"""
    data = data.copy()
    
    # 计算RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    signals = []
    position = 0
    
    for i in range(period, len(data)):
        if pd.isna(data['rsi'].iloc[i]):
            signals.append(0)
            continue
        
        # RSI超卖买入
        if data['rsi'].iloc[i] < oversold and position == 0:
            signals.append(1)
            position = 1
        # RSI超买卖出
        elif data['rsi'].iloc[i] > overbought and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, period)

def calculate_momentum_strategy(data, lookback, threshold):
    """动量策略"""
    data = data.copy()
    data['momentum'] = data['close'].pct_change(lookback)
    
    signals = []
    position = 0
    
    for i in range(lookback, len(data)):
        if pd.isna(data['momentum'].iloc[i]):
            signals.append(0)
            continue
        
        # 正动量买入
        if data['momentum'].iloc[i] > threshold and position == 0:
            signals.append(1)
            position = 1
        # 负动量卖出
        elif data['momentum'].iloc[i] < -threshold/2 and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, lookback)

def calculate_channel_strategy(data, period, threshold):
    """价格通道策略"""
    data = data.copy()
    data['high_max'] = data['high'].rolling(window=period).max()
    data['low_min'] = data['low'].rolling(window=period).min()
    data['channel_width'] = (data['high_max'] - data['low_min']) / data['close']
    
    signals = []
    position = 0
    
    for i in range(period, len(data)):
        if pd.isna(data['high_max'].iloc[i]) or pd.isna(data['low_min'].iloc[i]):
            signals.append(0)
            continue
        
        current_price = data['close'].iloc[i]
        
        # 突破上轨买入
        if current_price > data['high_max'].iloc[i-1] * (1 + threshold) and position == 0:
            signals.append(1)
            position = 1
        # 跌破下轨卖出
        elif current_price < data['low_min'].iloc[i-1] * (1 - threshold) and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, period)

def calculate_volatility_breakout_strategy(data, vol_period, vol_multiplier):
    """波动率突破策略"""
    data = data.copy()
    data['returns'] = data['close'].pct_change()
    data['volatility'] = data['returns'].rolling(window=vol_period).std()
    data['vol_threshold'] = data['volatility'] * vol_multiplier
    
    signals = []
    position = 0
    
    for i in range(vol_period, len(data)):
        if pd.isna(data['vol_threshold'].iloc[i]):
            signals.append(0)
            continue
        
        current_return = abs(data['returns'].iloc[i])
        
        # 波动率突破买入
        if current_return > data['vol_threshold'].iloc[i] and data['returns'].iloc[i] > 0 and position == 0:
            signals.append(1)
            position = 1
        # 反向波动率突破卖出
        elif current_return > data['vol_threshold'].iloc[i] and data['returns'].iloc[i] < 0 and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, vol_period)

def calculate_multi_confirmation_strategy(data):
    """多重确认策略"""
    data = data.copy()
    
    # 计算多个指标
    data['ma_fast'] = data['close'].rolling(window=3).mean()
    data['ma_slow'] = data['close'].rolling(window=8).mean()
    
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=7).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=7).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    data['momentum'] = data['close'].pct_change(2)
    
    signals = []
    position = 0
    
    for i in range(8, len(data)):
        if (pd.isna(data['ma_fast'].iloc[i]) or pd.isna(data['ma_slow'].iloc[i]) or 
            pd.isna(data['rsi'].iloc[i]) or pd.isna(data['momentum'].iloc[i])):
            signals.append(0)
            continue
        
        # 多重确认买入：MA金叉 + RSI超卖 + 正动量
        ma_bullish = (data['ma_fast'].iloc[i] > data['ma_slow'].iloc[i] and 
                     data['ma_fast'].iloc[i-1] <= data['ma_slow'].iloc[i-1])
        rsi_oversold = data['rsi'].iloc[i] < 35
        positive_momentum = data['momentum'].iloc[i] > 0.002
        
        # 多重确认卖出：MA死叉 或 RSI超买 或 负动量
        ma_bearish = (data['ma_fast'].iloc[i] < data['ma_slow'].iloc[i] and 
                     data['ma_fast'].iloc[i-1] >= data['ma_slow'].iloc[i-1])
        rsi_overbought = data['rsi'].iloc[i] > 65
        negative_momentum = data['momentum'].iloc[i] < -0.002
        
        if ma_bullish and rsi_oversold and positive_momentum and position == 0:
            signals.append(1)
            position = 1
        elif (ma_bearish or rsi_overbought or negative_momentum) and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, 8)

def calculate_strategy_returns(data, signals, start_idx):
    """计算策略收益率"""
    strategy_returns = []
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:
            current_position = 1
        elif signal == -1:
            current_position = 0
        
        if i + start_idx + 1 < len(data):
            price_return = ((data['close'].iloc[i + start_idx + 1] - 
                           data['close'].iloc[i + start_idx]) / 
                           data['close'].iloc[i + start_idx])
            strategy_return = price_return * current_position
            strategy_returns.append(strategy_return)
    
    return calculate_performance_metrics(strategy_returns)

def calculate_performance_metrics(returns):
    """计算绩效指标"""
    if len(returns) == 0:
        return {'annualized_return': 0, 'sharpe_ratio': 0, 'max_drawdown': 0, 'win_rate': 0, 'total_return': 0}
    
    returns = np.array(returns)
    
    # 总收益率
    total_return = np.prod(1 + returns) - 1
    
    # 年化收益率
    periods_per_year = 252 * 48
    annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
    
    # 夏普比率
    risk_free_rate = 0.03
    excess_returns = returns - risk_free_rate / periods_per_year
    sharpe_ratio = (excess_returns.mean() / excess_returns.std() * 
                   np.sqrt(periods_per_year) if excess_returns.std() != 0 else 0)
    
    # 最大回撤
    cumulative_returns = np.cumprod(1 + returns)
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)
    
    # 胜率
    positive_returns = returns[returns > 0]
    win_rate = len(positive_returns) / len(returns)
    
    return {
        'annualized_return': annualized_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'total_return': total_return
    }

if __name__ == "__main__":
    results = find_best_strategy()
