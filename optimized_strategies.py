import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def optimize_strategies():
    """优化策略参数"""
    data_path = "/Users/<USER>/Downloads/20241203IC/20241203IC_20250617.csv"
    
    print("正在加载数据...")
    df = pd.read_csv(data_path)
    df['datetime'] = pd.to_datetime(df['datetime'])
    df = df.sort_values('datetime').reset_index(drop=True)
    
    # 使用最近6个月数据（表现最好的时间段）
    test_data = df.tail(60000).copy()
    print(f"使用最近6个月数据: {len(test_data)} 行")
    print(f"时间范围: {test_data['datetime'].min()} 到 {test_data['datetime'].max()}")
    
    best_strategies = []
    
    # 1. 优化移动平均策略
    print(f"\n{'='*50}")
    print("优化移动平均策略参数")
    print(f"{'='*50}")
    
    ma_params = [
        (3, 10), (3, 15), (5, 15), (5, 20), (5, 25),
        (8, 21), (10, 30), (12, 26), (15, 35)
    ]
    
    best_ma_sharpe = -999
    best_ma_params = None
    
    for short, long in ma_params:
        result = calculate_ma_strategy_performance(test_data, short, long)
        sharpe = result['sharpe_ratio']
        ann_ret = result['annualized_return']
        
        print(f"MA({short},{long}): 年化收益={ann_ret:.4f}, 夏普比率={sharpe:.4f}")
        
        if sharpe > best_ma_sharpe:
            best_ma_sharpe = sharpe
            best_ma_params = (short, long)
            best_strategies.append(('MA_Optimized', result, f"MA({short},{long})"))
    
    print(f"最佳MA参数: {best_ma_params}, 夏普比率: {best_ma_sharpe:.4f}")
    
    # 2. 尝试组合策略
    print(f"\n{'='*50}")
    print("测试组合策略")
    print(f"{'='*50}")
    
    # MA + RSI组合策略
    combo_result = calculate_ma_rsi_combo_strategy(test_data, best_ma_params[0], best_ma_params[1], 30, 70)
    print(f"MA+RSI组合: 年化收益={combo_result['annualized_return']:.4f}, 夏普比率={combo_result['sharpe_ratio']:.4f}")
    best_strategies.append(('MA_RSI_Combo', combo_result, f"MA({best_ma_params[0]},{best_ma_params[1]})+RSI(30,70)"))
    
    # 3. 尝试反转策略
    print(f"\n{'='*50}")
    print("测试反转策略")
    print(f"{'='*50}")
    
    # 短期反转策略
    reversal_result = calculate_reversal_strategy(test_data, lookback=3, threshold=0.02)
    print(f"短期反转策略: 年化收益={reversal_result['annualized_return']:.4f}, 夏普比率={reversal_result['sharpe_ratio']:.4f}")
    best_strategies.append(('Short_Reversal', reversal_result, "短期反转(3日,2%)"))
    
    # 4. 尝试突破策略
    print(f"\n{'='*50}")
    print("测试突破策略")
    print(f"{'='*50}")
    
    # 价格突破策略
    breakout_result = calculate_breakout_strategy(test_data, lookback=20, threshold=0.015)
    print(f"突破策略: 年化收益={breakout_result['annualized_return']:.4f}, 夏普比率={breakout_result['sharpe_ratio']:.4f}")
    best_strategies.append(('Breakout', breakout_result, "突破策略(20日,1.5%)"))
    
    # 5. 尝试波动率策略
    print(f"\n{'='*50}")
    print("测试波动率策略")
    print(f"{'='*50}")
    
    volatility_result = calculate_volatility_strategy(test_data, vol_window=20, vol_threshold=0.02)
    print(f"波动率策略: 年化收益={volatility_result['annualized_return']:.4f}, 夏普比率={volatility_result['sharpe_ratio']:.4f}")
    best_strategies.append(('Volatility', volatility_result, "波动率策略(20日,2%)"))
    
    # 显示所有策略对比
    print(f"\n{'='*80}")
    print("所有优化策略对比")
    print(f"{'='*80}")
    print(f"{'策略':<20} {'描述':<25} {'年化收益':<12} {'夏普比率':<12} {'最大回撤':<12} {'胜率':<12}")
    print("-" * 93)
    
    best_overall_sharpe = -999
    best_overall_strategy = None
    
    for strategy_name, metrics, description in best_strategies:
        ann_ret = metrics['annualized_return']
        sharpe = metrics['sharpe_ratio']
        max_dd = metrics['max_drawdown']
        win_rate = metrics['win_rate']
        
        print(f"{strategy_name:<20} {description:<25} {ann_ret:<12.4f} {sharpe:<12.4f} {max_dd:<12.4f} {win_rate:<12.4f}")
        
        if sharpe > best_overall_sharpe:
            best_overall_sharpe = sharpe
            best_overall_strategy = (strategy_name, description)
    
    print(f"\n🏆 最佳策略: {best_overall_strategy[0]} - {best_overall_strategy[1]}")
    print(f"   夏普比率: {best_overall_sharpe:.4f}")
    
    if best_overall_sharpe > 1.5:
        print(f"🎉 恭喜！找到符合要求的策略，夏普比率 {best_overall_sharpe:.4f} > 1.5")
    else:
        print(f"⚠️  最佳策略夏普比率 {best_overall_sharpe:.4f} 仍未达到1.5目标")
        print("   建议：1) 尝试更短的时间周期 2) 考虑做空策略 3) 使用更复杂的机器学习方法")
    
    return best_strategies

def calculate_ma_strategy_performance(data, short_window, long_window):
    """计算移动平均策略绩效"""
    data = data.copy()
    data['ma_short'] = data['close'].rolling(window=short_window).mean()
    data['ma_long'] = data['close'].rolling(window=long_window).mean()
    
    signals = []
    position = 0
    
    for i in range(long_window, len(data)):
        if pd.isna(data['ma_short'].iloc[i]) or pd.isna(data['ma_long'].iloc[i]):
            signals.append(0)
            continue
            
        # 金叉买入
        if (data['ma_short'].iloc[i] > data['ma_long'].iloc[i] and 
            data['ma_short'].iloc[i-1] <= data['ma_long'].iloc[i-1] and position == 0):
            signals.append(1)
            position = 1
        # 死叉卖出
        elif (data['ma_short'].iloc[i] < data['ma_long'].iloc[i] and 
              data['ma_short'].iloc[i-1] >= data['ma_long'].iloc[i-1] and position == 1):
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, long_window)

def calculate_ma_rsi_combo_strategy(data, short_ma, long_ma, rsi_oversold, rsi_overbought):
    """MA + RSI组合策略"""
    data = data.copy()
    
    # 计算MA
    data['ma_short'] = data['close'].rolling(window=short_ma).mean()
    data['ma_long'] = data['close'].rolling(window=long_ma).mean()
    
    # 计算RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    signals = []
    position = 0
    
    for i in range(long_ma, len(data)):
        if (pd.isna(data['ma_short'].iloc[i]) or pd.isna(data['ma_long'].iloc[i]) or 
            pd.isna(data['rsi'].iloc[i])):
            signals.append(0)
            continue
        
        # 同时满足MA金叉和RSI超卖才买入
        ma_bullish = (data['ma_short'].iloc[i] > data['ma_long'].iloc[i] and 
                     data['ma_short'].iloc[i-1] <= data['ma_long'].iloc[i-1])
        rsi_oversold_signal = data['rsi'].iloc[i] < rsi_oversold
        
        # 同时满足MA死叉或RSI超买才卖出
        ma_bearish = (data['ma_short'].iloc[i] < data['ma_long'].iloc[i] and 
                     data['ma_short'].iloc[i-1] >= data['ma_long'].iloc[i-1])
        rsi_overbought_signal = data['rsi'].iloc[i] > rsi_overbought
        
        if ma_bullish and rsi_oversold_signal and position == 0:
            signals.append(1)
            position = 1
        elif (ma_bearish or rsi_overbought_signal) and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, long_ma)

def calculate_reversal_strategy(data, lookback, threshold):
    """短期反转策略"""
    data = data.copy()
    data['return'] = data['close'].pct_change(lookback)
    
    signals = []
    position = 0
    
    for i in range(lookback, len(data)):
        if pd.isna(data['return'].iloc[i]):
            signals.append(0)
            continue
        
        # 大跌后买入（反转）
        if data['return'].iloc[i] < -threshold and position == 0:
            signals.append(1)
            position = 1
        # 小幅上涨后卖出
        elif data['return'].iloc[i] > threshold/2 and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, lookback)

def calculate_breakout_strategy(data, lookback, threshold):
    """突破策略"""
    data = data.copy()
    data['high_max'] = data['high'].rolling(window=lookback).max()
    data['low_min'] = data['low'].rolling(window=lookback).min()
    
    signals = []
    position = 0
    
    for i in range(lookback, len(data)):
        if pd.isna(data['high_max'].iloc[i]) or pd.isna(data['low_min'].iloc[i]):
            signals.append(0)
            continue
        
        current_price = data['close'].iloc[i]
        high_breakout = current_price > data['high_max'].iloc[i-1] * (1 + threshold)
        low_breakdown = current_price < data['low_min'].iloc[i-1] * (1 - threshold)
        
        # 向上突破买入
        if high_breakout and position == 0:
            signals.append(1)
            position = 1
        # 向下突破卖出
        elif low_breakdown and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, lookback)

def calculate_volatility_strategy(data, vol_window, vol_threshold):
    """波动率策略"""
    data = data.copy()
    data['returns'] = data['close'].pct_change()
    data['volatility'] = data['returns'].rolling(window=vol_window).std()
    
    signals = []
    position = 0
    
    for i in range(vol_window, len(data)):
        if pd.isna(data['volatility'].iloc[i]):
            signals.append(0)
            continue
        
        # 低波动率时买入（预期波动率回升）
        if data['volatility'].iloc[i] < vol_threshold and position == 0:
            signals.append(1)
            position = 1
        # 高波动率时卖出
        elif data['volatility'].iloc[i] > vol_threshold * 2 and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    return calculate_strategy_returns(data, signals, vol_window)

def calculate_strategy_returns(data, signals, start_idx):
    """计算策略收益率"""
    strategy_returns = []
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:
            current_position = 1
        elif signal == -1:
            current_position = 0
        
        if i + start_idx + 1 < len(data):
            price_return = ((data['close'].iloc[i + start_idx + 1] - 
                           data['close'].iloc[i + start_idx]) / 
                           data['close'].iloc[i + start_idx])
            strategy_return = price_return * current_position
            strategy_returns.append(strategy_return)
    
    return calculate_performance_metrics(strategy_returns)

def calculate_performance_metrics(returns):
    """计算绩效指标"""
    if len(returns) == 0:
        return {'annualized_return': 0, 'sharpe_ratio': 0, 'max_drawdown': 0, 'win_rate': 0}
    
    returns = np.array(returns)
    
    # 总收益率
    total_return = np.prod(1 + returns) - 1
    
    # 年化收益率
    periods_per_year = 252 * 48
    annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
    
    # 夏普比率
    risk_free_rate = 0.03
    excess_returns = returns - risk_free_rate / periods_per_year
    sharpe_ratio = (excess_returns.mean() / excess_returns.std() * 
                   np.sqrt(periods_per_year) if excess_returns.std() != 0 else 0)
    
    # 最大回撤
    cumulative_returns = np.cumprod(1 + returns)
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)
    
    # 胜率
    positive_returns = returns[returns > 0]
    win_rate = len(positive_returns) / len(returns)
    
    return {
        'annualized_return': annualized_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'total_return': total_return
    }

if __name__ == "__main__":
    results = optimize_strategies()
