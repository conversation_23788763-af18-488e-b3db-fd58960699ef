import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_data(file_path):
    """加载并分析CSV数据"""
    print("正在加载数据...")
    
    # 尝试不同的编码方式加载数据
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
    df = None
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码加载数据")
            break
        except UnicodeDecodeError:
            continue
    
    if df is None:
        raise ValueError("无法加载数据文件，请检查文件路径和编码")
    
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print("\n前5行数据:")
    print(df.head())
    
    return df

def preprocess_data(df):
    """数据预处理"""
    print("\n开始数据预处理...")
    
    # 检查列名，可能的时间列名
    time_columns = ['time', 'timestamp', 'datetime', 'date', '时间', '日期']
    time_col = None
    
    for col in df.columns:
        if col.lower() in time_columns or 'time' in col.lower() or 'date' in col.lower():
            time_col = col
            break
    
    if time_col is None:
        # 如果没有找到时间列，假设第一列是时间
        time_col = df.columns[0]
        print(f"未找到明确的时间列，使用第一列作为时间列: {time_col}")
    
    # 转换时间列
    try:
        df[time_col] = pd.to_datetime(df[time_col])
        df = df.sort_values(time_col).reset_index(drop=True)
        print(f"成功转换时间列: {time_col}")
    except:
        print(f"时间列转换失败: {time_col}")
        return None
    
    # 检查OHLCV列
    ohlcv_mapping = {
        'open': ['open', 'Open', 'OPEN', '开盘价', '开盘'],
        'high': ['high', 'High', 'HIGH', '最高价', '最高'],
        'low': ['low', 'Low', 'LOW', '最低价', '最低'],
        'close': ['close', 'Close', 'CLOSE', '收盘价', '收盘'],
        'volume': ['volume', 'Volume', 'VOLUME', '成交量', '量']
    }
    
    column_mapping = {}
    for standard_name, possible_names in ohlcv_mapping.items():
        for col in df.columns:
            if col in possible_names:
                column_mapping[col] = standard_name
                break
    
    # 重命名列
    df = df.rename(columns=column_mapping)
    df = df.rename(columns={time_col: 'datetime'})
    
    # 确保必要的列存在
    required_cols = ['datetime', 'open', 'high', 'low', 'close']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        print(f"可用的列: {list(df.columns)}")
        return None
    
    # 转换数值列
    numeric_cols = ['open', 'high', 'low', 'close']
    if 'volume' in df.columns:
        numeric_cols.append('volume')
    
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 删除包含NaN的行
    df = df.dropna(subset=numeric_cols)
    
    print(f"预处理后数据形状: {df.shape}")
    print(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
    
    return df

def basic_statistics(df):
    """基础统计分析"""
    print("\n=== 基础统计分析 ===")
    
    # 价格统计
    price_stats = df[['open', 'high', 'low', 'close']].describe()
    print("价格统计:")
    print(price_stats)
    
    # 计算收益率
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    
    print(f"\n收益率统计:")
    print(f"平均收益率: {df['returns'].mean():.6f}")
    print(f"收益率标准差: {df['returns'].std():.6f}")
    print(f"最大单期收益: {df['returns'].max():.6f}")
    print(f"最大单期亏损: {df['returns'].min():.6f}")
    
    # 时间分析
    df['year'] = df['datetime'].dt.year
    df['month'] = df['datetime'].dt.month
    df['hour'] = df['datetime'].dt.hour
    
    print(f"\n时间分布:")
    print(f"年份范围: {df['year'].min()} - {df['year'].max()}")
    print(f"数据点数量按年份:")
    print(df['year'].value_counts().sort_index())
    
    return df

def split_data_by_year(df, output_dir='data_splits'):
    """按年份拆分数据"""
    import os
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print(f"\n按年份拆分数据到 {output_dir} 目录...")
    
    years = df['year'].unique()
    file_info = []
    
    for year in sorted(years):
        year_data = df[df['year'] == year].copy()
        filename = f"{output_dir}/data_{year}.csv"
        year_data.to_csv(filename, index=False)
        
        file_info.append({
            'year': year,
            'filename': filename,
            'records': len(year_data),
            'start_date': year_data['datetime'].min(),
            'end_date': year_data['datetime'].max()
        })
        
        print(f"  {year}: {len(year_data)} 条记录 -> {filename}")
    
    return file_info

def plot_price_analysis(df):
    """绘制价格分析图"""
    plt.figure(figsize=(15, 10))
    
    # 价格走势
    plt.subplot(2, 2, 1)
    plt.plot(df['datetime'], df['close'], linewidth=0.8)
    plt.title('价格走势')
    plt.xlabel('时间')
    plt.ylabel('价格')
    plt.xticks(rotation=45)
    
    # 收益率分布
    plt.subplot(2, 2, 2)
    plt.hist(df['returns'].dropna(), bins=100, alpha=0.7)
    plt.title('收益率分布')
    plt.xlabel('收益率')
    plt.ylabel('频次')
    
    # 成交量（如果有）
    if 'volume' in df.columns:
        plt.subplot(2, 2, 3)
        plt.plot(df['datetime'], df['volume'], linewidth=0.8)
        plt.title('成交量')
        plt.xlabel('时间')
        plt.ylabel('成交量')
        plt.xticks(rotation=45)
    
    # 价格波动率
    plt.subplot(2, 2, 4)
    df['volatility'] = df['returns'].rolling(window=20).std()
    plt.plot(df['datetime'], df['volatility'], linewidth=0.8)
    plt.title('20期滚动波动率')
    plt.xlabel('时间')
    plt.ylabel('波动率')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig('price_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 数据文件路径
    file_path = "/Users/<USER>/Downloads/20241203IC/20241203IC_20250617.csv"
    
    try:
        # 加载数据
        raw_df = load_and_analyze_data(file_path)
        
        # 预处理数据
        df = preprocess_data(raw_df)
        
        if df is not None:
            # 基础统计分析
            df = basic_statistics(df)
            
            # 按年份拆分数据
            file_info = split_data_by_year(df)
            
            # 绘制分析图
            plot_price_analysis(df)
            
            # 保存处理后的完整数据
            df.to_csv('processed_data.csv', index=False)
            print(f"\n处理后的完整数据已保存到: processed_data.csv")
            
            print("\n数据分析完成！")
            
        else:
            print("数据预处理失败，请检查数据格式")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
