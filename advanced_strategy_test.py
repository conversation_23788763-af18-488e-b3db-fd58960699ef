import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def test_multiple_periods_and_strategies():
    """测试多个时间段和策略"""
    data_path = "/Users/<USER>/Downloads/20241203IC/20241203IC_20250617.csv"
    
    print("正在加载数据...")
    df = pd.read_csv(data_path)
    df['datetime'] = pd.to_datetime(df['datetime'])
    df = df.sort_values('datetime').reset_index(drop=True)
    
    # 测试不同时间段
    test_periods = [
        ("最近6个月", 60000),
        ("最近1年", 120000),
        ("2023年数据", slice(-120000, -60000)),
        ("2022年数据", slice(-180000, -120000))
    ]
    
    all_results = {}
    
    for period_name, period_slice in test_periods:
        print(f"\n{'='*50}")
        print(f"测试时间段: {period_name}")
        print(f"{'='*50}")
        
        if isinstance(period_slice, int):
            test_data = df.tail(period_slice).copy()
        else:
            test_data = df.iloc[period_slice].copy()
        
        print(f"数据量: {len(test_data)} 行")
        print(f"时间范围: {test_data['datetime'].min()} 到 {test_data['datetime'].max()}")
        
        # 测试多种策略
        strategies_results = test_strategies_on_data(test_data)
        all_results[period_name] = strategies_results
        
        # 显示结果
        print(f"\n{period_name} - 策略绩效对比:")
        print(f"{'策略':<20} {'年化收益':<12} {'夏普比率':<12} {'最大回撤':<12} {'胜率':<12}")
        print("-" * 68)
        
        best_sharpe = -999
        best_strategy = None
        
        for strategy_name, metrics in strategies_results.items():
            ann_ret = metrics['annualized_return']
            sharpe = metrics['sharpe_ratio']
            max_dd = metrics['max_drawdown']
            win_rate = metrics['win_rate']
            
            print(f"{strategy_name:<20} {ann_ret:<12.4f} {sharpe:<12.4f} {max_dd:<12.4f} {win_rate:<12.4f}")
            
            if sharpe > best_sharpe:
                best_sharpe = sharpe
                best_strategy = strategy_name
        
        print(f"\n最佳策略: {best_strategy} (夏普比率: {best_sharpe:.4f})")
        if best_sharpe > 1.5:
            print(f"🎉 找到符合要求的策略！夏普比率 {best_sharpe:.4f} > 1.5")
    
    return all_results

def test_strategies_on_data(data):
    """在给定数据上测试多种策略"""
    results = {}
    
    # 1. Buy and Hold
    results['Buy_Hold'] = calculate_buy_hold_performance(data)
    
    # 2. 移动平均策略 - 不同参数
    results['MA_5_20'] = calculate_ma_strategy_performance(data, 5, 20)
    results['MA_10_30'] = calculate_ma_strategy_performance(data, 10, 30)
    results['MA_3_15'] = calculate_ma_strategy_performance(data, 3, 15)
    
    # 3. RSI策略 - 不同参数
    results['RSI_30_70'] = calculate_rsi_strategy_performance(data, 30, 70, 14)
    results['RSI_20_80'] = calculate_rsi_strategy_performance(data, 20, 80, 14)
    results['RSI_25_75'] = calculate_rsi_strategy_performance(data, 25, 75, 10)
    
    # 4. 布林带策略
    results['BB_20_2'] = calculate_bollinger_strategy_performance(data, 20, 2)
    results['BB_15_1.5'] = calculate_bollinger_strategy_performance(data, 15, 1.5)
    
    # 5. 动量策略
    results['Momentum_5'] = calculate_momentum_strategy_performance(data, 5)
    results['Momentum_10'] = calculate_momentum_strategy_performance(data, 10)
    
    return results

def calculate_performance_metrics(returns):
    """计算绩效指标"""
    if len(returns) == 0:
        return {'annualized_return': 0, 'sharpe_ratio': 0, 'max_drawdown': 0, 'win_rate': 0}
    
    returns = np.array(returns)
    
    # 总收益率
    total_return = np.prod(1 + returns) - 1
    
    # 年化收益率
    periods_per_year = 252 * 48
    annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
    
    # 夏普比率
    risk_free_rate = 0.03
    excess_returns = returns - risk_free_rate / periods_per_year
    sharpe_ratio = (excess_returns.mean() / excess_returns.std() * 
                   np.sqrt(periods_per_year) if excess_returns.std() != 0 else 0)
    
    # 最大回撤
    cumulative_returns = np.cumprod(1 + returns)
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)
    
    # 胜率
    positive_returns = returns[returns > 0]
    win_rate = len(positive_returns) / len(returns)
    
    return {
        'annualized_return': annualized_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'total_return': total_return
    }

def calculate_buy_hold_performance(data):
    """计算买入持有策略绩效"""
    close_prices = data['close'].values
    returns = []
    
    for i in range(1, len(close_prices)):
        if close_prices[i-1] != 0:
            ret = (close_prices[i] - close_prices[i-1]) / close_prices[i-1]
            returns.append(ret)
        else:
            returns.append(0)
    
    return calculate_performance_metrics(returns)

def calculate_ma_strategy_performance(data, short_window, long_window):
    """计算移动平均策略绩效"""
    data = data.copy()
    data['ma_short'] = data['close'].rolling(window=short_window).mean()
    data['ma_long'] = data['close'].rolling(window=long_window).mean()
    
    signals = []
    position = 0
    
    for i in range(long_window, len(data)):
        if pd.isna(data['ma_short'].iloc[i]) or pd.isna(data['ma_long'].iloc[i]):
            signals.append(0)
            continue
            
        # 金叉买入
        if (data['ma_short'].iloc[i] > data['ma_long'].iloc[i] and 
            data['ma_short'].iloc[i-1] <= data['ma_long'].iloc[i-1] and position == 0):
            signals.append(1)
            position = 1
        # 死叉卖出
        elif (data['ma_short'].iloc[i] < data['ma_long'].iloc[i] and 
              data['ma_short'].iloc[i-1] >= data['ma_long'].iloc[i-1] and position == 1):
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    # 计算策略收益
    strategy_returns = []
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:
            current_position = 1
        elif signal == -1:
            current_position = 0
        
        if i + long_window + 1 < len(data):
            price_return = ((data['close'].iloc[i + long_window + 1] - 
                           data['close'].iloc[i + long_window]) / 
                           data['close'].iloc[i + long_window])
            strategy_return = price_return * current_position
            strategy_returns.append(strategy_return)
    
    return calculate_performance_metrics(strategy_returns)

def calculate_rsi_strategy_performance(data, oversold, overbought, period):
    """计算RSI策略绩效"""
    data = data.copy()
    
    # 计算RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    signals = []
    position = 0
    
    for i in range(period, len(data)):
        if pd.isna(data['rsi'].iloc[i]):
            signals.append(0)
            continue
        
        # RSI超卖买入
        if data['rsi'].iloc[i] < oversold and position == 0:
            signals.append(1)
            position = 1
        # RSI超买卖出
        elif data['rsi'].iloc[i] > overbought and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    # 计算策略收益
    strategy_returns = []
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:
            current_position = 1
        elif signal == -1:
            current_position = 0
        
        if i + period + 1 < len(data):
            price_return = ((data['close'].iloc[i + period + 1] - 
                           data['close'].iloc[i + period]) / 
                           data['close'].iloc[i + period])
            strategy_return = price_return * current_position
            strategy_returns.append(strategy_return)
    
    return calculate_performance_metrics(strategy_returns)

def calculate_bollinger_strategy_performance(data, period, std_dev):
    """计算布林带策略绩效"""
    data = data.copy()
    
    # 计算布林带
    data['bb_middle'] = data['close'].rolling(window=period).mean()
    bb_std = data['close'].rolling(window=period).std()
    data['bb_upper'] = data['bb_middle'] + (bb_std * std_dev)
    data['bb_lower'] = data['bb_middle'] - (bb_std * std_dev)
    
    signals = []
    position = 0
    
    for i in range(period, len(data)):
        if pd.isna(data['bb_upper'].iloc[i]) or pd.isna(data['bb_lower'].iloc[i]):
            signals.append(0)
            continue
        
        price = data['close'].iloc[i]
        
        # 价格触及下轨买入
        if price <= data['bb_lower'].iloc[i] and position == 0:
            signals.append(1)
            position = 1
        # 价格触及上轨卖出
        elif price >= data['bb_upper'].iloc[i] and position == 1:
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    # 计算策略收益
    strategy_returns = []
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:
            current_position = 1
        elif signal == -1:
            current_position = 0
        
        if i + period + 1 < len(data):
            price_return = ((data['close'].iloc[i + period + 1] - 
                           data['close'].iloc[i + period]) / 
                           data['close'].iloc[i + period])
            strategy_return = price_return * current_position
            strategy_returns.append(strategy_return)
    
    return calculate_performance_metrics(strategy_returns)

def calculate_momentum_strategy_performance(data, lookback):
    """计算动量策略绩效"""
    data = data.copy()
    
    # 计算动量指标
    data['momentum'] = data['close'].pct_change(lookback)
    
    signals = []
    position = 0
    
    for i in range(lookback, len(data)):
        if pd.isna(data['momentum'].iloc[i]):
            signals.append(0)
            continue
        
        # 正动量买入
        if data['momentum'].iloc[i] > 0.01 and position == 0:  # 1%以上涨幅
            signals.append(1)
            position = 1
        # 负动量卖出
        elif data['momentum'].iloc[i] < -0.005 and position == 1:  # 0.5%以上跌幅
            signals.append(-1)
            position = 0
        else:
            signals.append(0)
    
    # 计算策略收益
    strategy_returns = []
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:
            current_position = 1
        elif signal == -1:
            current_position = 0
        
        if i + lookback + 1 < len(data):
            price_return = ((data['close'].iloc[i + lookback + 1] - 
                           data['close'].iloc[i + lookback]) / 
                           data['close'].iloc[i + lookback])
            strategy_return = price_return * current_position
            strategy_returns.append(strategy_return)
    
    return calculate_performance_metrics(strategy_returns)

if __name__ == "__main__":
    results = test_multiple_periods_and_strategies()
